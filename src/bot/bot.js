const { Telegraf } = require('telegraf');
const cron = require('node-cron');
const { config, validateConfig } = require('../config/config');
const NotificationService = require('../services/notificationService');
const RaidMonitor = require('../services/raidMonitor');

/**
 * Основной класс Telegram бота для мониторинга рейдов WoW
 */
class WoWRaidBot {
  constructor() {
    this.bot = null;
    this.notificationService = null;
    this.raidMonitor = null;
    this.cronJobs = [];
    this.isRunning = false;
  }

  /**
   * Инициализация бота
   */
  async initialize() {
    try {
      console.log('Инициализация WoW Raid Reminder Bot...');

      // Валидация конфигурации
      validateConfig();

      // Инициализация сервисов
      this.notificationService = new NotificationService();
      await this.notificationService.initialize();

      this.raidMonitor = new RaidMonitor(this.notificationService);
      await this.raidMonitor.initialize();

      // Создание Telegram бота
      this.bot = new Telegraf(config.telegram.botToken);

      // Настройка команд бота
      this.setupBotCommands();

      // Автоматический запуск планировщика задач
      this.setupScheduler();

      console.log('Бот успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации бота:', error.message);
      throw error;
    }
  }

  /**
   * Настройка команд бота
   */
  setupBotCommands() {
    // Команда /start
    this.bot.start((ctx) => {
      const welcomeMessage = `
🤖 <b>WoW Raid Reminder Bot</b>

Привет! Я бот для мониторинга коммерческих рейдов World of Warcraft.

<b>Мои функции:</b>
• 🔥 Мониторинг новых мифических рейдов
• 👥 Отслеживание заполненности рейдов
• ⚠️ Проверка соответствия типов рейдов
• 🕐 Контроль команд перед началом рейда
• 🎮 Напоминания об аккаунтах для рейдов

<b>Доступные команды:</b>
/status - Статус бота
/test - Тестовое сообщение
/stats - Статистика мониторинга
/filled - Таблица заполненности рейдов
/teams - Проверка составов команд
/piloted - Рейды с аккаунтами
/help - Помощь
/ignore <orderId> - игнорировать ошибки по заказу
      `;

      ctx.replyWithHTML(welcomeMessage);
    });

    // Команда /status
    this.bot.command('status', (ctx) => {
      const status = this.getStatus();
      ctx.replyWithHTML(status);
    });

    // Команда /test
    this.bot.command('test', async (ctx) => {
      const success = await this.notificationService.sendTestMessage();
      if (success) {
        ctx.reply('✅ Тестовое сообщение отправлено успешно');
      } else {
        ctx.reply('❌ Ошибка отправки тестового сообщения');
      }
    });

    // Команда /stats
    this.bot.command('stats', (ctx) => {
      const stats = this.getStats();
      ctx.replyWithHTML(stats);
    });

    // Команда /teams
    this.bot.command('teams', async (ctx) => {
      try {
        ctx.reply('🔍 Проверяю составы команд...');
        const result = await this.raidMonitor.checkAllTeamsManual();
        ctx.replyWithHTML(result);
      } catch (error) {
        console.error('Ошибка при проверке команд:', error.message);
        ctx.reply('❌ Ошибка при проверке команд. Попробуйте позже.');
      }
    });

    // Команда /ignore <orderId>
    this.bot.command('ignore', async (ctx) => {
      try {
        const text = (ctx.message && ctx.message.text) || '';
        console.log(`[BOT] Получена команда /ignore от ${ctx.chat?.id}: "${text}"`);
        const parts = text.trim().split(/\s+/);
        const orderId = parts[1];
        if (!orderId) {
          return ctx.reply('Использование: /ignore <номер_заказа>');
        }

        this.raidMonitor.ignoredOrdersStore.add(orderId);
        console.log(`[BOT] Добавлен в игнор-лист заказ: ${orderId}`);
        return ctx.reply(`✅ Заказ "${orderId}" добавлен в игнор-лист`);
      } catch (error) {
        console.error('Ошибка обработки команды /ignore:', error);
        return ctx.reply('❌ Не удалось добавить заказ в игнор-лист');
      }
    });

    // Команда /filled
    this.bot.command('filled', async (ctx) => {
      try {
        console.log('Получение статистики заполненности рейдов...');
        const stats = await this.raidMonitor.getRaidCapacityStats();

        if (stats.length === 0) {
          ctx.reply('📅 Нет рейдов начиная с сегодняшнего дня');
          return;
        }

        // Группируем рейды по дням
        const raidsByDay = {};
        stats.forEach(raid => {
          if (!raidsByDay[raid.date]) {
            raidsByDay[raid.date] = [];
          }
          raidsByDay[raid.date].push(raid);
        });

        // Создаем сообщения для каждого дня
        const dayNames = {
          'Monday': 'Понедельник',
          'Tuesday': 'Вторник',
          'Wednesday': 'Среда',
          'Thursday': 'Четверг',
          'Friday': 'Пятница',
          'Saturday': 'Суббота',
          'Sunday': 'Воскресенье'
        };

        for (const [date, raids] of Object.entries(raidsByDay)) {
          // Определяем день недели
          const dateObj = this.parseDisplayDate(date);
          const dayName = dayNames[dateObj.toLocaleDateString('en-US', { weekday: 'long' })] || 'День';

          // Создаем таблицу без HTML тегов внутри <pre>
          let message = `<b>📅 ${date}</b>\n`;
          message += `<b>${dayName} / ${dayName}</b>\n\n`;
          message += `<pre>`;
          message += `┌─────────────┬────────────────────────────────────────┐\n`;
          message += `│    Время    │                  Рейд                  │\n`;
          message += `├─────────────┼────────────────────────────────────────┤\n`;

          // Сортируем рейды по времени
          raids.sort((a, b) => a.time.localeCompare(b.time));

          raids.forEach(raid => {
            const time = `${raid.time} CEST`;
            let raidName = raid.raidType;

            // Добавляем информацию о клиентах (без лимита)
            let clientInfo = `${raid.clientCount}`;
            if (raid.lastBossCount > 0) {
              clientInfo += `+${raid.lastBossCount}л`;
            }

            // Добавляем составы команд
            let teamInfo = '';
            if (raid.teams && raid.teams.length > 0) {
              const uniqueTeams = [...new Set(raid.teams.map(t => t.team))];
              teamInfo = ` [${uniqueTeams.join(',')}]`;
            }

            // Форматируем название с информацией о клиентах и командах
            const fullName = `${raidName} (${clientInfo})${teamInfo}`;

            // Обрезаем длинные названия для таблицы
            const maxNameLength = 35; // Увеличиваем для команд
            let displayName = fullName;
            if (fullName.length > maxNameLength) {
              displayName = fullName.substring(0, maxNameLength - 3) + '...';
            }

            // Добавляем статус рейда (только для закрытых)
            const finalName = raid.status === 'закрыт' ? `🔒 ${displayName}` : displayName;

            // Добавляем пробелы для выравнивания
            const paddedTime = time.padEnd(11);

            // Фиксированная ширина для правой колонки - 38 символов
            // Для закрытых рейдов нужно на 1 символ меньше из-за эмодзи
            let paddedName;
            if (raid.status === 'закрыт') {
              // Для закрытых рейдов: эмодзи + пробел + текст + пробелы = 38 символов
              const nameLength = displayName.length;
              const spacesNeeded = Math.max(0, 35 - nameLength); // 38 - 2 (эмодзи + пробел)
              paddedName = finalName + ' '.repeat(spacesNeeded);
            } else {
              paddedName = finalName.padEnd(38);
            }

            message += `│ ${paddedTime} │ ${paddedName} │\n`;
          });

          message += `└─────────────┴────────────────────────────────────────┘`;
          message += `</pre>`;

          await ctx.replyWithHTML(message);
        }

      } catch (error) {
        console.error('Ошибка получения заполненности рейдов:', error.message);
        ctx.reply('❌ Ошибка получения заполненности рейдов');
      }
    });

    // Команда /piloted
    this.bot.command('piloted', async (ctx) => {
      try {
        console.log('Получение списка рейдов с аккаунтами...');
        const raidsWithAccounts = await this.raidMonitor.sheetsService.getUpcomingRaidsWithAccounts();

        if (raidsWithAccounts.length === 0) {
          ctx.reply('📅 Нет предстоящих рейдов с аккаунтами');
          return;
        }

        // Получаем текущее время в CET
        const now = new Date();
        const cetTime = new Date(now.toLocaleString('en-US', { timeZone: 'Europe/Berlin' }));
        const currentTimeString = cetTime.toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        });
        const currentDateString = `${cetTime.getDate().toString().padStart(2, '0')}.${(cetTime.getMonth() + 1).toString().padStart(2, '0')}.${cetTime.getFullYear()}`;

        // Форматируем сообщение
        let message = `<b>🎮 Ближайшие рейды с шарами:</b>\n`;
        message += `<b>⏰ Текущее время CET: ${currentDateString} ${currentTimeString}</b>\n\n`;
        
        raidsWithAccounts.forEach(raid => {
          // Форматируем дату в нужном формате
          const dateParts = raid.date.split('.');
          if (dateParts.length === 3) {
            const formattedDate = `${dateParts[0]}.${dateParts[1]}.${dateParts[2]}`;
            message += `${formattedDate} ${raid.time} ${raid.raidType} - ${raid.clientCount}\n`;
          } else {
            message += `${raid.date} ${raid.time} ${raid.raidType} - ${raid.clientCount}\n`;
          }
        });

        ctx.replyWithHTML(message);
      } catch (error) {
        console.error('Ошибка получения рейдов с аккаунтами:', error.message);
        ctx.reply('❌ Ошибка получения рейдов с аккаунтами');
      }
    });

    // Команда /help
    this.bot.command('help', (ctx) => {
      const helpMessage = `
<b>📖 Справка по командам:</b>

/start - Приветствие и основная информация
/status - Показать текущий статус бота
/test - Отправить тестовое сообщение
/stats - Показать статистику мониторинга
/filled - Показать заполненность рейдов
/piloted - Показать рейды с аккаунтами
/help - Показать эту справку
/ignore <orderId> - Добавить заказ в игнор-лист ошибок

<b>🔧 Настройки мониторинга:</b>
• Проверка каждые ${config.monitoring.checkIntervalMinutes} минут
• Уведомление о мифических/ATP/FP рейдах через ${config.monitoring.mythicNotificationDelayMinutes} минут
• Проверка команд за ${config.monitoring.preRaidCheckMinutes} минут до рейда
• Напоминание об аккаунтах за ${config.monitoring.accountsReminderMinutes} минут до рейда
• Порог заполненности: ${config.monitoring.minClientsThreshold} клиентов

<b>⏰ Временная зона:</b> ${config.timezone}
      `;

      ctx.replyWithHTML(helpMessage);
    });

    // Обработка ошибок
    this.bot.catch((err, ctx) => {
      console.error('Ошибка в Telegram боте:', err);
      ctx.reply('❌ Произошла ошибка при обработке команды');
    });
  }

  /**
   * Запуск бота и планировщика задач
   */
  async start() {
    try {
      if (this.isRunning) {
        console.log('Бот уже запущен');
        return;
      }

      console.log('Запуск бота...');

      // Запуск Telegram бота
      await this.bot.launch();
      console.log('Telegram бот запущен');

      // Планировщик уже настроен в initialize(), просто проверяем
      if (this.cronJobs.length === 0) {
        this.setupScheduler();
      }

      // Выполняем первоначальную проверку
      console.log('Выполняется первоначальная проверка...');
      await this.raidMonitor.performCheck();

      // Отправка уведомления о запуске
      await this.notificationService.sendNotification(
        '🚀 <b>WoW Raid Reminder Bot запущен</b>\n\n' +
        `⏰ Мониторинг активен\n` +
        `🔄 Проверка каждые ${config.monitoring.checkIntervalMinutes} минут`
      );

      this.isRunning = true;
      console.log('Бот успешно запущен и готов к работе');

      // Graceful shutdown
      process.once('SIGINT', () => this.stop('SIGINT'));
      process.once('SIGTERM', () => this.stop('SIGTERM'));

    } catch (error) {
      console.error('Ошибка запуска бота:', error.message);
      throw error;
    }
  }

  /**
   * Настройка планировщика задач
   */
  setupScheduler() {
    // Основная проверка каждые N минут
    const checkInterval = `*/${config.monitoring.checkIntervalMinutes} * * * *`;

    const mainCheckJob = cron.schedule(checkInterval, async () => {
      console.log('Выполняется плановая проверка рейдов...');
      await this.raidMonitor.performCheck();
    }, {
      scheduled: false,
      timezone: config.timezone,
    });

    this.cronJobs.push(mainCheckJob);
    mainCheckJob.start();

    console.log(`Планировщик настроен: проверка каждые ${config.monitoring.checkIntervalMinutes} минут`);
  }

  /**
   * Остановка бота
   */
  async stop(signal = 'MANUAL') {
    try {
      console.log(`Получен сигнал остановки: ${signal}`);

      if (!this.isRunning) {
        console.log('Бот уже остановлен');
        return;
      }

      // Отправка уведомления об остановке
      if (this.notificationService) {
        await this.notificationService.sendNotification(
          '🛑 <b>WoW Raid Reminder Bot остановлен</b>\n\n' +
          `📊 Причина: ${signal}\n` +
          `⏰ ${new Date().toLocaleString('ru-RU', { timeZone: config.timezone })}`
        );
      }

      // Остановка cron задач
      this.cronJobs.forEach(job => {
        job.stop();
        job.destroy();
      });


      this.cronJobs = [];

      // Очистка таймеров мифических рейдов
      if (this.raidMonitor) {
        this.raidMonitor.cleanup();
      }

      // Остановка Telegram бота
      if (this.bot) {
        this.bot.stop(signal);
      }

      this.isRunning = false;
      console.log('Бот успешно остановлен');

    } catch (error) {
      console.error('Ошибка при остановке бота:', error.message);
    }
  }

  /**
   * Получение статуса бота
   */
  getStatus() {
    const uptime = process.uptime();
    const uptimeFormatted = this.formatUptime(uptime);

    // Подсчитываем все активные задачи
    const mainCronJobs = this.cronJobs.length;
    const dynamicCronJobs = this.raidMonitor?.dynamicCronJobs?.size || 0;
    const totalCronJobs = mainCronJobs + dynamicCronJobs;

    // Определяем статус: если сервисы инициализированы и есть cron задачи, то активен
    const isActive = this.isRunning || (
      this.notificationService?.initialized &&
      this.raidMonitor?.initialized &&
      totalCronJobs > 0
    );

    return `
<b>🤖 Статус WoW Raid Reminder Bot</b>

<b>🔄 Состояние:</b> ${isActive ? '✅ Активен' : '❌ Остановлен'}
<b>⏰ Время работы:</b> ${uptimeFormatted}
<b>📊 Активных cron задач:</b> ${totalCronJobs} (основных: ${mainCronJobs}, динамических: ${dynamicCronJobs})

<b>🔧 Сервисы:</b>
• Уведомления: ${this.notificationService?.initialized ? '✅' : '❌'}
• Мониторинг рейдов: ${this.raidMonitor?.initialized ? '✅' : '❌'}

<b>⚙️ Настройки:</b>
• Интервал проверки: ${config.monitoring.checkIntervalMinutes} мин
• Задержка уведомлений: ${config.monitoring.mythicNotificationDelayMinutes} мин
• Проверка команд: за ${config.monitoring.preRaidCheckMinutes} мин
• Порог заполненности: ${config.monitoring.minClientsThreshold} клиентов
    `;
  }

  /**
   * Получение статистики мониторинга
   */
  getStats() {
    const raidStats = this.raidMonitor?.getStats() || {};
    const notificationStats = this.notificationService?.getStats() || {};

    return `
<b>📊 Статистика мониторинга</b>

<b>🔥 Активные таймеры уведомлений:</b>
• Мифические рейды: ${raidStats.activeMythicTimers || 0}
• ATP рейды: ${raidStats.activeATPTimers || 0}
• FP рейды: ${raidStats.activeFPTimers || 0}

<b>⏰ Динамические проверки команд:</b>
• Активных cron-задач: ${raidStats.dynamicCronJobs || 0}

<b>👥 Клиенты:</b>
• Записей в последней проверке: ${raidStats.previousClientsCount || 0}

<b>📨 Уведомления:</b>
• Отправленных сообщений: ${notificationStats.sentMessagesCount || 0}

<b>💾 Память:</b>
• Использовано: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} МБ
• Всего доступно: ${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)} МБ
    `;
  }

  /**
   * Форматирование времени работы
   */
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    const parts = [];
    if (days > 0) parts.push(`${days}д`);
    if (hours > 0) parts.push(`${hours}ч`);
    if (minutes > 0) parts.push(`${minutes}м`);

    return parts.length > 0 ? parts.join(' ') : 'менее минуты';
  }

  /**
   * Проверка здоровья бота
   */
  async healthCheck() {
    const checks = {
      bot: this.isRunning,
      notification: this.notificationService?.initialized || false,
      raidMonitor: this.raidMonitor?.initialized || false,
      cronJobs: this.cronJobs.length > 0,
    };

    const allHealthy = Object.values(checks).every(check => check === true);

    return {
      healthy: allHealthy,
      checks,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Парсинг даты из формата DD.MM.YYYY
   */
  parseDisplayDate(dateStr) {
    const [day, month, year] = dateStr.split('.');
    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  }
}

module.exports = WoWRaidBot;