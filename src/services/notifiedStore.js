const fs = require('fs');
const path = require('path');

class NotifiedStore {
  constructor() {
    this.filePath = path.join(__dirname, '..', 'data', 'notified.json');
    this.data = {
      заказы: [], // Простой список всех заказов
      specialRaidNotifications: {
        mythic: [],
        atp: [],
        fp: []
      },
      previousClients: [] // Сохраняем предыдущих клиентов для сравнения
    };
  }

  async initialize() {
    try {
      // Ensure directory exists
      const dir = path.dirname(this.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      if (fs.existsSync(this.filePath)) {
        const raw = fs.readFileSync(this.filePath, 'utf8');
        const parsed = JSON.parse(raw || '{}');
        this.data.заказы = Array.isArray(parsed.заказы) ? parsed.заказы : [];

        // Восстанавливаем уведомления
        const notificationsArray = Array.isArray(parsed.notifications) ? parsed.notifications : [];
        this.data.notifications = new Set(notificationsArray);

        // Восстанавливаем специальные уведомления о рейдах
        if (parsed.specialRaidNotifications) {
          this.data.specialRaidNotifications = {
            mythic: Array.isArray(parsed.specialRaidNotifications.mythic) ? parsed.specialRaidNotifications.mythic : [],
            atp: Array.isArray(parsed.specialRaidNotifications.atp) ? parsed.specialRaidNotifications.atp : [],
            fp: Array.isArray(parsed.specialRaidNotifications.fp) ? parsed.specialRaidNotifications.fp : []
          };
        }

        // Восстанавливаем предыдущих клиентов
        if (parsed.previousClients && Array.isArray(parsed.previousClients)) {
          this.data.previousClients = parsed.previousClients;
        }
      }
    } catch (e) {
      console.error('Ошибка при инициализации NotifiedStore:', e);
      // Start fresh on error
      this.data = {
        заказы: [],
        notifications: new Set(),
        specialRaidNotifications: {
          mythic: [],
          atp: [],
          fp: []
        },
        previousClients: []
      };
    }
  }

  save() {
    // Сохраняем уведомления как массив
    const dataToSave = { ...this.data };
    if (this.data.notifications) {
      dataToSave.notifications = Array.from(this.data.notifications);
    }
    fs.writeFileSync(this.filePath, JSON.stringify(dataToSave, null, 2), 'utf8');
  }

  // Проверяет, есть ли заказ в списке
  has(key) {
    return this.data.заказы.some(order => order.key === key);
  }

  // Добавляет заказ в список
  add(key, date, time) {
    if (!this.has(key)) {
      this.data.заказы.push({ key, date, time });
      this.save();
    }
  }

  // Добавляет заказ в список с метаданными
  addWithMetadata(key, date, time, metadata) {
    if (!this.has(key)) {
      this.data.заказы.push({ key, date, time, metadata });
      this.save();
    }
  }

  // Добавляет несколько заказов
  bulkAdd(orders) {
    for (const order of orders) {
      if (!this.has(order.key)) {
        this.data.заказы.push(order);
      }
    }
    this.save();
  }

  // Удаляет заказ из списка
  remove(key) {
    this.data.заказы = this.data.заказы.filter(order => order.key !== key);
    this.save();
  }

  // Получает заказ по ключу
  get(key) {
    return this.data.заказы.find(order => order.key === key);
  }

  // Получает все заказы
  getAll() {
    return this.data.заказы;
  }

  // Очищает все заказы
  clear() {
    this.data.заказы = [];
    this.save();
  }

  // Инициализирует список заказов (заменяет все существующие)
  initializeOrders(orders) {
    this.data.заказы = orders;
    this.save();
  }

  // Проверяет, изменился ли заказ (сравнивает дату и время)
  hasChanged(key, date, time) {
    const order = this.get(key);
    if (!order) return false; // Заказ не найден
    return order.date !== date || order.time !== time;
  }

  // Обновляет заказ (если он изменился)
  update(key, date, time) {
    const order = this.get(key);
    if (order) {
      order.date = date;
      order.time = time;
      this.save();
    }
  }

  // Обновляет заказ с метаданными (если он изменился)
  updateWithMetadata(key, date, time, metadata) {
    const order = this.get(key);
    if (order) {
      order.date = date;
      order.time = time;
      order.metadata = metadata;
      this.save();
    }
  }

  // Методы для отслеживания уведомлений
  isNotified(notificationKey) {
    // Убеждаемся, что notifications существует
    if (!this.data.notifications) {
      this.data.notifications = new Set();
    }
    return this.data.notifications.has(notificationKey);
  }

  markAsNotified(notificationKey) {
    // Убеждаемся, что notifications существует
    if (!this.data.notifications) {
      this.data.notifications = new Set();
    }
    this.data.notifications.add(notificationKey);
    this.save();
  }

  // Методы для работы со специальными уведомлениями о рейдах

  /**
   * Проверка, было ли отправлено уведомление о специальном рейде
   */
  isSpecialRaidNotified(raidType, date) {
    if (!this.data.specialRaidNotifications || !this.data.specialRaidNotifications[raidType]) {
      return false;
    }
    return this.data.specialRaidNotifications[raidType].includes(date);
  }

  /**
   * Отметка о том, что уведомление о специальном рейде отправлено
   */
  markSpecialRaidAsNotified(raidType, date) {
    // Убеждаемся, что структура существует
    if (!this.data.specialRaidNotifications) {
      this.data.specialRaidNotifications = {
        mythic: [],
        atp: [],
        fp: []
      };
    }

    if (!this.data.specialRaidNotifications[raidType]) {
      this.data.specialRaidNotifications[raidType] = [];
    }

    if (!this.data.specialRaidNotifications[raidType].includes(date)) {
      this.data.specialRaidNotifications[raidType].push(date);
      this.save();
    }
  }

  /**
   * Получение предыдущих клиентов
   */
  getPreviousClients() {
    return this.data.previousClients || [];
  }

  /**
   * Сохранение текущих клиентов как предыдущих
   */
  setPreviousClients(clients) {
    this.data.previousClients = clients;
    this.save();
  }
}

module.exports = NotifiedStore;


