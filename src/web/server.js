const express = require('express');
const session = require('express-session');
const FileStore = require('session-file-store')(session);
const path = require('path');
const fs = require('fs').promises;
const { config } = require('../config/config');
const ConfigSheetsService = require('../services/configSheetsService');

/**
 * Веб-сервер для управления конфигурацией бота
 */
class ConfigWebServer {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    this.configPath = path.join(__dirname, '../config/dynamic-config.json');
    this.configSheetsService = new ConfigSheetsService();
    this.useGoogleSheets = process.env.USE_GOOGLE_SHEETS_CONFIG === 'true';
  }

  /**
   * Инициализация веб-сервера
   */
  async initialize() {
    // Настройка middleware
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.join(__dirname, 'public')));

    // Настройка сессий
    this.app.use(session({
      secret: process.env.SESSION_SECRET || 'wow-raid-bot-secret',
      resave: true,
      saveUninitialized: true,
      cookie: {
        secure: false, // Отключаем secure для локальной разработки
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 часа
      },
      store: new FileStore({
        path: './sessions', // Путь для хранения сессий
        ttl: 24 * 60 * 60, // Время жизни сессии в секундах
        reapInterval: 60 * 60 // Интервал сбора мусора в секундах
      })
    }));

    // Инициализируем Google Sheets сервис если используем его
    if (this.useGoogleSheets) {
      try {
        await this.configSheetsService.initialize();
        console.log('Google Sheets конфигурация инициализирована');
      } catch (error) {
        console.error('Ошибка инициализации Google Sheets конфигурации:', error.message);
        this.useGoogleSheets = false; // Fallback к файловой системе
      }
    }

    // Настройка маршрутов
    this.setupRoutes();

    console.log('Веб-сервер конфигурации инициализирован');
  }

  /**
   * Настройка маршрутов
   */
  setupRoutes() {
    // Главная страница - редирект на админку
    this.app.get('/', (req, res) => {
      res.redirect('/admin');
    });

    // Страница входа
    this.app.get('/admin', (req, res) => {
      console.log('GET /admin - ID сессии:', req.sessionID);
      console.log('GET /admin - сессия:', req.session);
      console.log('GET /admin - проверка аутентификации:', req.session.authenticated ? 'АУТЕНТИФИЦИРОВАН' : 'НЕ АУТЕНТИФИЦИРОВАН');

      // Заголовки для предотвращения кэширования
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      if (req.session.authenticated) {
        console.log('Отправка admin.html');
        res.sendFile(path.join(__dirname, 'public', 'admin.html'));
      } else {
        console.log('Отправка login.html');
        res.sendFile(path.join(__dirname, 'public', 'login.html'));
      }
    });

    // Аутентификация
    this.app.post('/admin/login', (req, res) => {
      const { password } = req.body;

      console.log('Попытка входа с паролем:', password ? '[СКРЫТ]' : 'ПУСТОЙ');
      console.log('Ожидаемый пароль:', this.adminPassword ? '[УСТАНОВЛЕН]' : 'НЕ УСТАНОВЛЕН');
      console.log('ID сессии до аутентификации:', req.sessionID);
      console.log('Сессия до аутентификации:', req.session);

      if (password === this.adminPassword) {
        req.session.authenticated = true;
        console.log('Успешная аутентификация');
        console.log('ID сессии после аутентификации:', req.sessionID);
        console.log('Сессия после аутентификации:', req.session);

        // Принудительно сохраняем сессию
        req.session.save((err) => {
          if (err) {
            console.error('Ошибка сохранения сессии:', err);
            return res.status(500).json({ success: false, message: 'Ошибка сохранения сессии' });
          }
          console.log('Сессия успешно сохранена');
          res.json({ success: true });
        });
      } else {
        console.log('Неверный пароль');
        res.status(401).json({ success: false, message: 'Неверный пароль' });
      }
    });

    // Выход
    this.app.post('/admin/logout', (req, res) => {
      req.session.destroy();
      res.json({ success: true });
    });

    // Middleware для проверки аутентификации
    const requireAuth = (req, res, next) => {
      if (!req.session.authenticated) {
        return res.status(401).json({ success: false, message: 'Требуется аутентификация' });
      }
      next();
    };

    // API для получения текущей конфигурации
    this.app.get('/api/config', requireAuth, async (req, res) => {
      try {
        const dynamicConfig = await this.loadDynamicConfig();
        res.json({
          success: true,
          config: dynamicConfig
        });
      } catch (error) {
        console.error('Ошибка загрузки конфигурации:', error);
        res.status(500).json({ 
          success: false, 
          message: 'Ошибка загрузки конфигурации' 
        });
      }
    });

    // API для сохранения конфигурации
    this.app.post('/api/config', requireAuth, async (req, res) => {
      try {
        const newConfig = req.body;
        await this.saveDynamicConfig(newConfig);
        
        res.json({
          success: true,
          message: 'Конфигурация успешно сохранена'
        });
      } catch (error) {
        console.error('Ошибка сохранения конфигурации:', error);
        res.status(500).json({ 
          success: false, 
          message: 'Ошибка сохранения конфигурации' 
        });
      }
    });

    // API для перезапуска бота (если нужно)
    this.app.post('/api/restart', requireAuth, (req, res) => {
      res.json({
        success: true,
        message: 'Для применения изменений перезапустите бота'
      });
    });

    // API эндпоинт для получения информации о командах (аналог команды /teams)
    this.app.get('/teams', async (req, res) => {
      try {
        // Проверяем авторизацию через токен
        const authToken = req.query.authorization || req.headers.authorization;
        const expectedToken = process.env.AUTH_TOKEN;

        if (!authToken || authToken !== expectedToken) {
          return res.status(401).json({
            success: false,
            error: 'Unauthorized',
            message: 'Неверный токен авторизации'
          });
        }

        // Получаем экземпляр бота для доступа к raidMonitor
        if (!this.botInstance || !this.botInstance.raidMonitor) {
          return res.status(503).json({
            success: false,
            error: 'Service Unavailable',
            message: 'Сервис мониторинга рейдов недоступен'
          });
        }

        // Вызываем метод проверки команд
        const teamsData = await this.getTeamsDataForAPI();

        res.json({
          success: true,
          data: teamsData
        });

      } catch (error) {
        console.error('Ошибка API /teams:', error.message);
        res.status(500).json({
          success: false,
          error: 'Internal Server Error',
          message: 'Внутренняя ошибка сервера'
        });
      }
    });
  }

  /**
   * Загрузка динамической конфигурации
   */
  async loadDynamicConfig() {
    try {
      if (this.useGoogleSheets) {
        console.log('Загрузка конфигурации из Google Sheets...');
        return await this.configSheetsService.loadConfig();
      } else {
        console.log('Загрузка конфигурации из файла...');
        const data = await fs.readFile(this.configPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn('Ошибка загрузки конфигурации:', error.message);
      // Если не удалось загрузить, создаем конфигурацию по умолчанию
      const defaultConfig = this.getDefaultDynamicConfig();

      if (this.useGoogleSheets) {
        try {
          await this.configSheetsService.saveConfig(defaultConfig);
          console.log('Создана конфигурация по умолчанию в Google Sheets');
        } catch (saveError) {
          console.error('Ошибка создания конфигурации по умолчанию:', saveError.message);
        }
      } else {
        await this.saveDynamicConfig(defaultConfig);
      }

      return defaultConfig;
    }
  }

  /**
   * Сохранение динамической конфигурации
   */
  async saveDynamicConfig(configData) {
    if (this.useGoogleSheets) {
      console.log('Сохранение конфигурации в Google Sheets...');
      await this.configSheetsService.saveConfig(configData);
    } else {
      console.log('Сохранение конфигурации в файл...');
      await fs.writeFile(this.configPath, JSON.stringify(configData, null, 2), 'utf8');
    }
  }

  /**
   * Получение конфигурации по умолчанию
   */
  getDefaultDynamicConfig() {
    return {
      monitoring: {
        checkIntervalMinutes: config.monitoring.checkIntervalMinutes,
        mythicNotificationDelayMinutes: config.monitoring.mythicNotificationDelayMinutes,
        preRaidCheckMinutes: config.monitoring.preRaidCheckMinutes,
        minClientsThreshold: config.monitoring.minClientsThreshold
      },
      allowedRaidTypes: config.allowedRaidTypes,
      teamCapacityLimits: config.teamCapacityLimits,
      lastBossRaidTypes: config.lastBossRaidTypes
    };
  }

  /**
   * Запуск сервера
   */
  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`Веб-сервер конфигурации запущен на порту ${this.port}`);
        console.log(`Админ-панель доступна по адресу: http://localhost:${this.port}/admin`);
        resolve();
      });
    });
  }

  /**
   * Получение данных команд для API (аналог команды /teams)
   */
  async getTeamsDataForAPI() {
    try {
      if (!this.botInstance || !this.botInstance.raidMonitor) {
        throw new Error('Сервис мониторинга рейдов недоступен');
      }

      const raidMonitor = this.botInstance.raidMonitor;

      if (!raidMonitor.initialized) {
        throw new Error('Сервис мониторинга не инициализирован');
      }

      console.log('Получение данных команд для API...');

      // Получаем сырые данные для JSON ответа
      const [clients, schedule] = await Promise.all([
        raidMonitor.sheetsService.getClientsData(),
        raidMonitor.scheduleService.getCombinedScheduleData(),
      ]);

      const internalSchedule = await raidMonitor.internalScheduleService.getInternalScheduleData();
      const DateUtils = require('../utils/dateUtils');
      const RaidUtils = require('../utils/raidUtils');

      // Фильтруем рейды начиная с сегодняшнего дня
      const relevantRaids = schedule.filter(raid => {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) return false;
        return DateUtils.isDateTodayOrFuture(raidDateTime);
      });

      const raidStats = [];

      // Проверяем каждый рейд на проблемы с командами
      for (const raid of relevantRaids) {
        // Получаем клиентов для этого рейда
        const raidClients = clients.filter(client =>
          client.date === raid.date &&
          client.time === raid.time &&
          RaidUtils.isRaidTypeAllowed(raid.raidType, client.raidType)
        );

        // Получаем информацию о командах
        const teamInfo = await raidMonitor.getTeamInfoForRaid(
          raid.date,
          raid.time,
          raid.raidType,
          internalSchedule,
          raid
        );

        // Группируем клиентов по командам
        const teamClientCounts = {};
        raidClients.forEach(client => {
          if (client.team && client.team.trim()) {
            const teamName = client.team.trim();
            teamClientCounts[teamName] = (teamClientCounts[teamName] || 0) + 1;
          }
        });

        // Создаем массив команд с количеством клиентов
        const expectedTeams = (teamInfo.teams || []).map(teamData => ({
          team: teamData.team,
          clientCount: teamClientCounts[teamData.team] || 0
        }));

        const raidStat = {
          date: raid.date,
          time: raid.time,
          raidType: raid.raidType,
          expectedTeams: expectedTeams
        };

        raidStats.push(raidStat);
      }

      return raidStats;

    } catch (error) {
      console.error('Ошибка получения данных команд для API:', error.message);
      throw error;
    }
  }

  /**
   * Установка экземпляра бота для доступа к raidMonitor
   */
  setBotInstance(botInstance) {
    this.botInstance = botInstance;
  }

  /**
   * Остановка сервера
   */
  stop() {
    if (this.server) {
      this.server.close();
      console.log('Веб-сервер конфигурации остановлен');
    }
  }
}

module.exports = ConfigWebServer;
