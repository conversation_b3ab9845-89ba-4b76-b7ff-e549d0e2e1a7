const { config } = require('../config/config');
const DateUtils = require('./dateUtils');

/**
 * Утилиты для работы с рейдами
 */
class RaidUtils {
  /**
   * Проверка, является ли рейд мифическим
   */
  static isMythicRaid(raidType) {
    if (!raidType) return false;
    return raidType.toLowerCase().includes('mythic');
  }

  /**
   * Группировка клиентов по составу, дате и времени
   */
  static groupClientsByRaid(clients) {
    const groups = {};

    for (const client of clients) {
      // Создаем ключ для группировки: дата + время + тип рейда
      const key = `${client.date}|${client.time}|${client.raidType}`;

      if (!groups[key]) {
        groups[key] = {
          date: client.date,
          time: client.time,
          raidType: client.raidType,
          clients: [],
          teams: new Set(),
        };
      }

      groups[key].clients.push(client);

      // Добавляем команду в множество (если указана)
      if (client.team && client.team.trim()) {
        groups[key].teams.add(client.team.trim());
      }
    }

    // Преобразуем множества команд в массивы
    Object.values(groups).forEach(group => {
      group.teams = Array.from(group.teams);
      group.clientCount = group.clients.length;
    });

    return groups;
  }

  /**
   * Проверка соответствия типа рейда допустимым типам
   */
  static isRaidTypeAllowed(scheduleRaidType, clientRaidType, allowedRaidTypes = null) {
    // Используем переданную конфигурацию или статическую
    const allowedTypes = allowedRaidTypes ? allowedRaidTypes[scheduleRaidType] : config.allowedRaidTypes[scheduleRaidType];

    // Проверяем точное совпадение
    if (scheduleRaidType === clientRaidType) {
      return true;
    }

    // Проверяем список допустимых типов
    if (allowedTypes && allowedTypes.includes(clientRaidType)) {
      return true;
    }

    // Гибкое сопоставление для Mythic рейдов
    const scheduleIsMythic = scheduleRaidType.toLowerCase().includes('mythic');
    const clientIsMythic = clientRaidType.toLowerCase().includes('mythic');

    if (scheduleIsMythic && clientIsMythic) {
      return true;
    }

    // Если нет настроенных допустимых типов, используем только точное совпадение
    if (!allowedTypes) {
      return false;
    }

    return false;
  }

  /**
   * Получение всех недопустимых типов рейдов для клиентов
   */
  static findRaidTypeMismatches(scheduleRaids, clientGroups, allowedRaidTypes = null, ignoredOrdersStore = null) {
    const mismatches = [];

    // Игнор-лист типов рейдов, которые не нужно проверять
    const ignoredRaidTypes = ['Key', 'KeyH', 'Additional payment'];

    // Текущее время для фильтрации старых рейдов
    const now = new Date();
    const threeHoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 часа назад

    // Сначала проверяем все ATP/FP правила по дате и времени
    const atpFpViolations = this.checkATPFPViolationsByDateTime(clientGroups, ignoredOrdersStore);

    // Добавляем нарушения ATP/FP правил, но только для рейдов не старше 3 часов
    atpFpViolations.forEach(violation => {
      const raidDateTime = DateUtils.createDateTime(violation.date, violation.time);
      if (raidDateTime && raidDateTime.isValid() && raidDateTime.toDate() >= threeHoursAgo) {
        // Ищем расписание для этого времени
        const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
          scheduleRaid.date === violation.date && scheduleRaid.time === violation.time
        );

        mismatches.push({
          scheduleRaidType: matchingScheduleRaids.map(r => r.raidType).join(', ') || 'Не найдено в расписании',
          clientRaidType: 'Смешанные типы',
          date: violation.date,
          time: violation.time,
          clientCount: violation.clients.length,
          violationType: violation.type,
          violationMessage: violation.message,
          clientsDetailed: violation.clientsDetailed
        });
      }
    });

    // Проверяем каждую группу клиентов
    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');

      // Игнорируем определенные типы рейдов
      if (ignoredRaidTypes.includes(clientRaidType)) {
        continue;
      }

      // Проверяем, что рейд не старше 3 часов
      const raidDateTime = DateUtils.createDateTime(clientDate, clientTime);
      if (!raidDateTime || !raidDateTime.isValid() || raidDateTime.toDate() < threeHoursAgo) {
        continue; // Пропускаем старые рейды
      }

      // Ищем все рейды в расписании с такой же датой и временем
      const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
        scheduleRaid.date === clientDate && scheduleRaid.time === clientTime
      );

      if (matchingScheduleRaids.length === 0) {
        // Если в расписании нет рейдов на это время, это не ошибка типа
        continue;
      }

      // Фильтруем клиентов по игнор-листу
      const filteredClients = ignoredOrdersStore
        ? clientGroup.clients.filter(c => !(c.orderId && ignoredOrdersStore.has(c.orderId)))
        : clientGroup.clients;

      if (filteredClients.length === 0) {
        continue;
      }

      // Создаем временную группу с отфильтрованными клиентами для проверки ATP/FP правил
      const filteredClientGroup = {
        ...clientGroup,
        clients: filteredClients
      };

      // Проверяем ATP/FP правила
      const atpFpCheck = this.checkATPFPRules(filteredClientGroup);

      // Если есть ATP/FP клиенты и они соответствуют правилам, не отправляем уведомление
      if (atpFpCheck.hasATPFPClients && atpFpCheck.isValid) {
        continue;
      }

      // Если есть нарушения ATP/FP правил, добавляем их как отдельные несоответствия
      if (atpFpCheck.hasATPFPClients && !atpFpCheck.isValid) {
        atpFpCheck.violations.forEach(violation => {
          mismatches.push({
            scheduleRaidType: matchingScheduleRaids.map(r => r.raidType).join(', '),
            clientRaidType: clientRaidType,
            date: clientDate,
            time: clientTime,
            clientCount: filteredClients.length,
            violationType: violation.type,
            violationMessage: violation.message,
            clientsDetailed: filteredClients.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId,
              clientRaidType: client.raidType,
            })),
          });
        });
        continue;
      }

      // Проверяем, подходит ли тип клиентов хотя бы к одному рейду в расписании (обычная логика)
      const hasMatchingRaid = matchingScheduleRaids.some(scheduleRaid =>
        this.isRaidTypeAllowed(scheduleRaid.raidType, clientRaidType, allowedRaidTypes)
      );

      if (!hasMatchingRaid) {
        // Если ни один рейд в расписании не подходит, это несоответствие
        const scheduleRaidTypes = matchingScheduleRaids.map(r => r.raidType).join(', ');

        mismatches.push({
          scheduleRaidType: scheduleRaidTypes,
          clientRaidType: clientRaidType,
          date: clientDate,
          time: clientTime,
          clientCount: filteredClients.length,
          clientsDetailed: filteredClients.map(client => ({
            rowNumber: client.rowNumber,
            orderId: client.orderId,
            clientRaidType: client.raidType,
          })),
        });
      }
    }

    return mismatches;
  }

  /**
   * Форсированная проверка типов рейдов (для команды /teams)
   * Игнорирует только старые рейды (>3 часов), но сохраняет игнор-лист типов
   */
  static findRaidTypeMismatchesForced(scheduleRaids, clientGroups, allowedRaidTypes = null, ignoredOrdersStore = null) {
    const mismatches = [];

    // Игнор-лист типов рейдов, которые не нужно проверять
    const ignoredRaidTypes = ['Key', 'KeyH', 'Additional payment'];

    // Текущее время для фильтрации старых рейдов
    const now = new Date();
    const threeHoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 часа назад

    // Сначала проверяем все ATP/FP правила по дате и времени
    const atpFpViolations = this.checkATPFPViolationsByDateTime(clientGroups, ignoredOrdersStore);

    // Добавляем нарушения ATP/FP правил, но только для рейдов не старше 3 часов
    atpFpViolations.forEach(violation => {
      const raidDateTime = DateUtils.createDateTime(violation.date, violation.time);
      if (raidDateTime && raidDateTime.isValid() && raidDateTime.toDate() >= threeHoursAgo) {
        // Ищем расписание для этого времени
        const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
          scheduleRaid.date === violation.date && scheduleRaid.time === violation.time
        );

        mismatches.push({
          scheduleRaidType: matchingScheduleRaids.map(r => r.raidType).join(', ') || 'Не найдено в расписании',
          clientRaidType: 'Смешанные типы',
          date: violation.date,
          time: violation.time,
          clientCount: violation.clients.length,
          violationType: violation.type,
          violationMessage: violation.message,
          clientsDetailed: violation.clientsDetailed
        });
      }
    });

    // Проверяем каждую группу клиентов
    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');

      // Игнорируем определенные типы рейдов
      if (ignoredRaidTypes.includes(clientRaidType)) {
        continue;
      }

      // Проверяем, что рейд не старше 3 часов
      const raidDateTime = DateUtils.createDateTime(clientDate, clientTime);
      if (!raidDateTime || !raidDateTime.isValid() || raidDateTime.toDate() < threeHoursAgo) {
        continue; // Пропускаем старые рейды
      }

      // Ищем все рейды в расписании с такой же датой и временем
      const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
        scheduleRaid.date === clientDate && scheduleRaid.time === clientTime
      );

      if (matchingScheduleRaids.length === 0) {
        // Если в расписании нет рейдов на это время, это не ошибка типа
        continue;
      }

      // Фильтруем клиентов по игнор-листу
      const filteredClients = ignoredOrdersStore
        ? clientGroup.clients.filter(c => !(c.orderId && ignoredOrdersStore.has(c.orderId)))
        : clientGroup.clients;

      if (filteredClients.length === 0) {
        continue;
      }

      // Создаем временную группу с отфильтрованными клиентами для проверки ATP/FP правил
      const filteredClientGroup = {
        ...clientGroup,
        clients: filteredClients
      };

      // Проверяем ATP/FP правила
      const atpFpCheck = this.checkATPFPRules(filteredClientGroup);

      // Если есть ATP/FP клиенты и они соответствуют правилам, не отправляем уведомление
      if (atpFpCheck.hasATPFPClients && atpFpCheck.isValid) {
        continue;
      }

      // Если есть нарушения ATP/FP правил, добавляем их как отдельные несоответствия
      if (atpFpCheck.hasATPFPClients && !atpFpCheck.isValid) {
        atpFpCheck.violations.forEach(violation => {
          mismatches.push({
            scheduleRaidType: matchingScheduleRaids.map(r => r.raidType).join(', '),
            clientRaidType: clientRaidType,
            date: clientDate,
            time: clientTime,
            clientCount: filteredClients.length,
            violationType: violation.type,
            violationMessage: violation.message,
            clientsDetailed: filteredClients.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId,
              clientRaidType: client.raidType,
            })),
          });
        });
        continue;
      }

      // Проверяем, подходит ли тип клиентов хотя бы к одному рейду в расписании (обычная логика)
      const hasMatchingRaid = matchingScheduleRaids.some(scheduleRaid =>
        this.isRaidTypeAllowed(scheduleRaid.raidType, clientRaidType, allowedRaidTypes)
      );

      if (!hasMatchingRaid) {
        // Если ни один рейд в расписании не подходит, это несоответствие
        const scheduleRaidTypes = matchingScheduleRaids.map(r => r.raidType).join(', ');

        mismatches.push({
          scheduleRaidType: scheduleRaidTypes,
          clientRaidType: clientRaidType,
          date: clientDate,
          time: clientTime,
          clientCount: filteredClients.length,
          clientsDetailed: filteredClients.map(client => ({
            rowNumber: client.rowNumber,
            orderId: client.orderId,
            clientRaidType: client.raidType,
          })),
        });
      }
    }

    return mismatches;
  }

  /**
   * Создание объекта Date из строки даты и времени
   */
  static createDateTime(dateStr, timeStr) {
    try {
      const [day, month, year] = dateStr.split('.');
      const [hours, minutes] = timeStr.split(':');

      return new Date(
        parseInt(year),
        parseInt(month) - 1, // месяцы в JS начинаются с 0
        parseInt(day),
        parseInt(hours),
        parseInt(minutes)
      );
    } catch (error) {
      console.error(`Ошибка парсинга даты/времени: ${dateStr} ${timeStr}`, error);
      return null;
    }
  }

  /**
   * Проверка команд для рейда (упрощенная - только на пустое значение)
   */
  static checkTeamAssignments(scheduleRaid, clients, ignoredOrdersStore = null) {
    const issues = [];

    // scheduleRaid не используется в упрощенной проверке, но оставляем для совместимости API
    for (const client of clients) {
      // Игнорируем клиентов из игнор-листа по заказу
      if (ignoredOrdersStore && client.orderId && ignoredOrdersStore.has(client.orderId)) {
        continue;
      }
      if (!client.team || client.team.trim() === '') {
        const orderInfo = client.orderId ? `Заказ: "${client.orderId}" ` : '';
        issues.push(`${orderInfo}Клиент без назначенной команды (строка ${client.rowNumber})`);
      }
      // Убираем проверку на соответствие конкретной команде, так как в расписании нет информации о составе
    }

    return issues;
  }

  /**
   * Поиск новых мифических рейдов
   */
  static findNewMythicRaids(currentClients, previousClients = []) {
    const currentMythicRaids = new Set();
    const previousMythicRaids = new Set();

    // Собираем текущие мифические рейды
    for (const client of currentClients) {
      if (this.isMythicRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        currentMythicRaids.add(key);
      }
    }

    // Собираем предыдущие мифические рейды
    for (const client of previousClients) {
      if (this.isMythicRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        previousMythicRaids.add(key);
      }
    }

    // Находим новые рейды (есть в текущих, но нет в предыдущих)
    const newRaids = [];
    for (const raidKey of currentMythicRaids) {
      if (!previousMythicRaids.has(raidKey)) {
        const [date, time, raidType] = raidKey.split('|');
        newRaids.push({ date, time, raidType });
      }
    }

    return newRaids;
  }

  /**
   * Поиск новых ATP рейдов
   */
  static findNewATPRaids(currentClients, previousClients = []) {
    const currentATPRaids = new Set();
    const previousATPRaids = new Set();

    // Собираем текущие ATP рейды
    for (const client of currentClients) {
      if (this.isATPRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        currentATPRaids.add(key);
      }
    }

    // Собираем предыдущие ATP рейды
    for (const client of previousClients) {
      if (this.isATPRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        previousATPRaids.add(key);
      }
    }

    // Находим новые рейды (есть в текущих, но нет в предыдущих)
    const newRaids = [];
    for (const raidKey of currentATPRaids) {
      if (!previousATPRaids.has(raidKey)) {
        const [date, time, raidType] = raidKey.split('|');
        newRaids.push({ date, time, raidType });
      }
    }

    return newRaids;
  }

  /**
   * Поиск новых FP рейдов
   */
  static findNewFPRaids(currentClients, previousClients = []) {
    const currentFPRaids = new Set();
    const previousFPRaids = new Set();

    // Собираем текущие FP рейды
    for (const client of currentClients) {
      if (this.isFPRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        currentFPRaids.add(key);
      }
    }

    // Собираем предыдущие FP рейды
    for (const client of previousClients) {
      if (this.isFPRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        previousFPRaids.add(key);
      }
    }

    // Находим новые рейды (есть в текущих, но нет в предыдущих)
    const newRaids = [];
    for (const raidKey of currentFPRaids) {
      if (!previousFPRaids.has(raidKey)) {
        const [date, time, raidType] = raidKey.split('|');
        newRaids.push({ date, time, raidType });
      }
    }

    return newRaids;
  }

  /**
   * Нормализация названия команды
   */
  static normalizeTeamName(teamName) {
    if (!teamName) return '';
    return teamName.trim().toLowerCase();
  }

  /**
   * Проверка, заполнен ли рейд (достиг ли порогового количества клиентов)
   */
  static isRaidFull(clientCount, threshold = null) {
    const minThreshold = threshold || config.monitoring.minClientsThreshold;
    return clientCount >= minThreshold;
  }

  /**
   * Проверка, является ли тип рейда ATP
   */
  static isATPRaid(raidType) {
    if (!raidType) return false;
    return raidType.includes('ATP');
  }

  /**
   * Проверка, является ли тип рейда FP
   */
  static isFPRaid(raidType) {
    if (!raidType) return false;
    return raidType.includes('FP');
  }

  /**
   * Получение сложности рейда ATP/FP (Normal или Heroic)
   */
  static getRaidDifficulty(raidType) {
    if (!raidType) return null;

    if (raidType.includes('Normal')) return 'Normal';
    if (raidType.includes('Heroic')) return 'Heroic';

    return null;
  }

  /**
   * Получение количества игроков из типа рейда ATP/FP
   */
  static getPlayerCount(raidType) {
    if (!raidType) return null;

    const match = raidType.match(/(\d+)\s*ppl/i);
    return match ? parseInt(match[1]) : null;
  }

  /**
   * Проверка, является ли тип рейда обычным (не ATP и не FP)
   */
  static isRegularRaid(raidType) {
    if (!raidType) return false;
    return !this.isATPRaid(raidType) && !this.isFPRaid(raidType);
  }

  /**
   * Проверка совместимости типов рейдов в одной группе
   * Возвращает объект с информацией о нарушениях
   */
  static checkRaidTypeMixingViolations(clientGroup) {
    const violations = [];
    const clients = clientGroup.clients || [];

    if (clients.length === 0) {
      return { isValid: true, violations: [] };
    }

    // Группируем клиентов по типам рейдов
    const atpClients = clients.filter(c => this.isATPRaid(c.raidType));
    const fpClients = clients.filter(c => this.isFPRaid(c.raidType));
    const regularClients = clients.filter(c => this.isRegularRaid(c.raidType));

    // Проверка 1: ATP не должны смешиваться с FP
    if (atpClients.length > 0 && fpClients.length > 0) {
      violations.push({
        type: 'atp_fp_mixing',
        message: 'ATP и FP рейды не могут быть в одной группе',
        atpCount: atpClients.length,
        fpCount: fpClients.length
      });
    }

    // Проверка 2: ATP не должны смешиваться с обычными рейдами
    if (atpClients.length > 0 && regularClients.length > 0) {
      violations.push({
        type: 'atp_regular_mixing',
        message: 'ATP рейды не могут смешиваться с обычными рейдами',
        atpCount: atpClients.length,
        regularCount: regularClients.length
      });
    }

    // Проверка 3: FP не должны смешиваться с обычными рейдами
    if (fpClients.length > 0 && regularClients.length > 0) {
      violations.push({
        type: 'fp_regular_mixing',
        message: 'FP рейды не могут смешиваться с обычными рейдами',
        fpCount: fpClients.length,
        regularCount: regularClients.length
      });
    }

    return {
      isValid: violations.length === 0,
      violations: violations,
      atpClients: atpClients,
      fpClients: fpClients,
      regularClients: regularClients
    };
  }

  /**
   * Проверка ограничений по количеству клиентов ATP/FP
   */
  static checkClientCountLimits(clientGroup) {
    const violations = [];
    const clients = clientGroup.clients || [];

    if (clients.length === 0) {
      return { isValid: true, violations: [] };
    }

    const atpClients = clients.filter(c => this.isATPRaid(c.raidType));
    const fpClients = clients.filter(c => this.isFPRaid(c.raidType));

    // Проверка 1: Максимум 1 клиент FP в рейде
    if (fpClients.length > 1) {
      violations.push({
        type: 'fp_count_limit',
        message: `Превышен лимит FP клиентов: ${fpClients.length} (максимум 1)`,
        currentCount: fpClients.length,
        maxCount: 1,
        clients: fpClients
      });
    }

    // Проверка 2: Максимум 4 клиента ATP в рейде
    if (atpClients.length > 4) {
      violations.push({
        type: 'atp_count_limit',
        message: `Превышен лимит ATP клиентов: ${atpClients.length} (максимум 4)`,
        currentCount: atpClients.length,
        maxCount: 4,
        clients: atpClients
      });
    }

    return {
      isValid: violations.length === 0,
      violations: violations,
      atpCount: atpClients.length,
      fpCount: fpClients.length
    };
  }

  /**
   * Проверка сложности ATP рейдов (все должны быть одной сложности)
   */
  static checkATPDifficultyConsistency(clientGroup) {
    const violations = [];
    const clients = clientGroup.clients || [];

    const atpClients = clients.filter(c => this.isATPRaid(c.raidType));

    if (atpClients.length <= 1) {
      return { isValid: true, violations: [] };
    }

    // Группируем ATP клиентов по сложности
    const difficultiesMap = {};
    atpClients.forEach(client => {
      const difficulty = this.getRaidDifficulty(client.raidType);
      if (difficulty) {
        if (!difficultiesMap[difficulty]) {
          difficultiesMap[difficulty] = [];
        }
        difficultiesMap[difficulty].push(client);
      }
    });

    const difficulties = Object.keys(difficultiesMap);

    // Проверяем, что все ATP клиенты одной сложности
    if (difficulties.length > 1) {
      violations.push({
        type: 'atp_difficulty_mismatch',
        message: `ATP клиенты должны быть одной сложности, найдено: ${difficulties.join(', ')}`,
        difficulties: difficulties,
        clientsByDifficulty: difficultiesMap
      });
    }

    return {
      isValid: violations.length === 0,
      violations: violations,
      difficulties: difficulties,
      clientsByDifficulty: difficultiesMap
    };
  }

  /**
   * Комплексная проверка правил ATP/FP для группы клиентов
   */
  static checkATPFPRules(clientGroup) {
    const allViolations = [];

    // Проверка смешивания типов
    const mixingCheck = this.checkRaidTypeMixingViolations(clientGroup);
    if (!mixingCheck.isValid) {
      allViolations.push(...mixingCheck.violations);
    }

    // Проверка ограничений по количеству
    const countCheck = this.checkClientCountLimits(clientGroup);
    if (!countCheck.isValid) {
      allViolations.push(...countCheck.violations);
    }

    // Проверка сложности ATP
    const difficultyCheck = this.checkATPDifficultyConsistency(clientGroup);
    if (!difficultyCheck.isValid) {
      allViolations.push(...difficultyCheck.violations);
    }

    return {
      isValid: allViolations.length === 0,
      violations: allViolations,
      hasATPFPClients: mixingCheck.atpClients?.length > 0 || mixingCheck.fpClients?.length > 0,
      details: {
        mixing: mixingCheck,
        count: countCheck,
        difficulty: difficultyCheck
      }
    };
  }

  /**
   * Проверка всех ATP/FP правил по дате, времени и команде
   * Объединяет клиентов с одинаковой датой, временем и командой для проверки правил.
   * ВАЖНО: Разные команды в одно время считаются разными рейдами и НЕ проверяются на смешивание.
   * Смешивание проверяется только внутри одной команды.
   */
  static checkATPFPViolationsByDateTime(clientGroups, ignoredOrdersStore = null) {
    const violations = [];

    // Группируем клиентов по дате, времени и команде (игнорируя тип рейда)
    const dateTimeTeamGroups = {};

    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');

      // Проверяем, что у группы есть клиенты
      if (!clientGroup.clients || !Array.isArray(clientGroup.clients)) {
        continue;
      }

      // Фильтруем клиентов по игнор-листу
      const filteredClients = ignoredOrdersStore
        ? clientGroup.clients.filter(c => !(c.orderId && ignoredOrdersStore.has(c.orderId)))
        : clientGroup.clients;

      // Группируем по команде внутри каждой группы клиентов
      filteredClients.forEach(client => {
        const team = client.team || 'NO_TEAM'; // Используем 'NO_TEAM' для клиентов без команды
        const dateTimeTeamKey = `${clientDate}|${clientTime}|${team}`;

        if (!dateTimeTeamGroups[dateTimeTeamKey]) {
          dateTimeTeamGroups[dateTimeTeamKey] = {
            date: clientDate,
            time: clientTime,
            team: team,
            clients: []
          };
        }

        dateTimeTeamGroups[dateTimeTeamKey].clients.push(client);
      });
    }

    // Проверяем каждую группу по дате/времени/команде на все ATP/FP правила
    for (const [dateTimeTeamKey, group] of Object.entries(dateTimeTeamGroups)) {
      if (group.clients.length === 0) continue;

      // Проверяем смешивание типов
      const mixingCheck = this.checkRaidTypeMixingViolations(group);
      if (!mixingCheck.isValid) {
        mixingCheck.violations.forEach(violation => {
          violations.push({
            ...violation,
            date: group.date,
            time: group.time,
            clients: group.clients,
            clientsDetailed: group.clients.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId,
              clientRaidType: client.raidType,
            }))
          });
        });
      }

      // Проверяем ограничения по количеству
      const countCheck = this.checkClientCountLimits(group);
      if (!countCheck.isValid) {
        countCheck.violations.forEach(violation => {
          violations.push({
            ...violation,
            date: group.date,
            time: group.time,
            clients: group.clients,
            clientsDetailed: group.clients.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId,
              clientRaidType: client.raidType,
            }))
          });
        });
      }

      // Проверяем сложность ATP
      const difficultyCheck = this.checkATPDifficultyConsistency(group);
      if (!difficultyCheck.isValid) {
        difficultyCheck.violations.forEach(violation => {
          violations.push({
            ...violation,
            date: group.date,
            time: group.time,
            clients: group.clients,
            clientsDetailed: group.clients.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId,
              clientRaidType: client.raidType,
            }))
          });
        });
      }
    }

    return violations;
  }

  /**
   * Получение краткого описания рейда
   */
  static getRaidDescription(date, time, raidType) {
    return `${raidType} ${date} ${time}`;
  }

  /**
   * Сортировка рейдов по дате и времени
   */
  static sortRaidsByDateTime(raids) {
    return raids.sort((a, b) => {
      // Сначала по дате
      const dateComparison = a.date.localeCompare(b.date);
      if (dateComparison !== 0) return dateComparison;

      // Затем по времени
      return a.time.localeCompare(b.time);
    });
  }

  /**
   * Получение лимита рейда по умолчанию (без mythic)
   */
  static getDefaultRaidLimit(raidType) {
    const raidTypeLower = raidType.toLowerCase();

    if (raidTypeLower.includes('heroic')) {
      return config.raidCapacityLimits.heroic || 25;
    } else if (raidTypeLower.includes('normal')) {
      return config.raidCapacityLimits.normal || 30;
    }

    return config.raidCapacityLimits.default || 20;
  }

  /**
   * Проверка дублирующихся никнеймов в одном рейде
   * Проверяет, что в рейде с одинаковыми датой, временем и составом нет повторяющихся никнеймов
   * @param {Object} clientGroups - Группы клиентов по рейдам
   * @param {Object} ignoredOrdersStore - Хранилище игнорируемых заказов
   */
  static checkDuplicateNicknames(clientGroups, ignoredOrdersStore = null) {
    const duplicates = [];

    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');
      const clients = clientGroup.clients || [];

      if (clients.length <= 1) {
        continue; // Нет смысла проверять группы с одним клиентом или без клиентов
      }

      // Собираем никнеймы из поля Q (индекс 16 в rawData)
      const nicknameMap = new Map(); // nickname -> array of clients with this nickname

      clients.forEach(client => {
        // Пропускаем игнорируемые заказы
        if (ignoredOrdersStore && client.orderId && ignoredOrdersStore.has(client.orderId)) {
          return;
        }

        let nickname = '';

        // Извлекаем никнейм из поля Q (индекс 16)
        try {
          if (Array.isArray(client.rawData) && client.rawData.length > 16) {
            nickname = (client.rawData[16] || '').toString().trim();
          }
        } catch (_) {
          // Игнорируем ошибки парсинга
        }

        // Пропускаем пустые никнеймы
        if (!nickname) {
          return;
        }

        if (!nicknameMap.has(nickname)) {
          nicknameMap.set(nickname, []);
        }
        nicknameMap.get(nickname).push(client);
      });

      // Ищем никнеймы, которые встречаются более одного раза
      for (const [nickname, clientsWithNickname] of nicknameMap.entries()) {
        if (clientsWithNickname.length > 1) {
          duplicates.push({
            nickname: nickname,
            count: clientsWithNickname.length,
            date: clientDate,
            time: clientTime,
            raidType: clientRaidType,
            clients: clientsWithNickname.map(client => ({
              rowNumber: client.rowNumber,
              orderId: client.orderId
            }))
          });
        }
      }
    }

    return duplicates;
  }
}

module.exports = RaidUtils;