# Проверка пустых строк в именованных диапазонах

## Описание

Добавлена новая функциональность для проверки количества пустых строк в именованных диапазонах Google Sheets `currentWeek` и `partialWeek`. Система автоматически отправляет уведомления, если в диапазоне осталось меньше 10 пустых строк с конца.

## Критерии пустой строки

Строка считается пустой, если **все** следующие столбцы не заполнены (пустые или содержат только пробелы):
- **B** (индекс 1) - Дата
- **E** (индекс 4) - Номер заказа  
- **G** (индекс 6) - Дополнительная информация
- **O** (индекс 14) - Дополнительные данные
- **Q** (индекс 16) - Статус
- **T** (индекс 19) - Время
- **U** (индекс 20) - Команда
- **V** (индекс 21) - Тип рейда

## Проверяемые диапазоны

### currentWeek
- **Описание**: Диапазон для текущей недели
- **Порог уведомления**: < 10 пустых строк с конца
- **Отображаемое имя**: "Текущая неделя"

### partialWeek  
- **Описание**: Диапазон для частичной недели
- **Порог уведомления**: < 10 пустых строк с конца
- **Отображаемое имя**: "Частичная неделя"

## Интеграция в систему мониторинга

Проверка выполняется в рамках основного цикла мониторинга каждые 10 минут (настраивается через `CHECK_INTERVAL_MINUTES`).

### Последовательность проверок:
1. Проверка новых специальных рейдов
2. Проверка заказов с прошедшими датами
3. Проверка заказов с прошедшим временем
4. Проверка заполненности рейдов
5. Проверка соответствия типов рейдов
6. **Проверка пустых строк в диапазонах** ← новая функциональность
7. Проверка соответствия команд
8. Обновление динамических задач

## Формат уведомления

```
⚠️ Недостаток пустых строк в диапазонах:

📊 Текущая неделя (currentWeek)
   • Пустых строк с конца: 8
   • Требуется минимум: 10
   • Общее количество строк: 150

📊 Частичная неделя (partialWeek)
   • Пустых строк с конца: 5
   • Требуется минимум: 10
   • Общее количество строк: 100

Строка считается пустой, если столбцы B, E, G, O, Q, T, U, V не заполнены
```

## Технические детали

### Новые методы в SheetsService:

#### `getNamedRangeData(spreadsheetId, rangeName)`
- Получает данные из именованного диапазона
- Возвращает массив строк

#### `checkEmptyRowsInNamedRange(spreadsheetId, rangeName)`
- Проверяет количество пустых строк с конца диапазона
- Возвращает объект с информацией о диапазоне

#### `checkEmptyRowsInWeekRanges()`
- Проверяет оба диапазона (currentWeek и partialWeek)
- Возвращает объект с данными по обоим диапазонам

### Новые методы в RaidMonitor:

#### `checkEmptyRowsInWeekRanges()`
- Выполняет проверку и определяет проблемные диапазоны
- Возвращает массив проблем

#### `formatEmptyRowsNotification(issues)`
- Форматирует уведомление для отправки
- Возвращает HTML-форматированное сообщение

## Настройка именованных диапазонов

В Google Sheets необходимо создать именованные диапазоны:

1. Откройте Google Sheets
2. Выделите нужный диапазон ячеек
3. Перейдите в меню "Данные" → "Именованные диапазоны"
4. Создайте диапазон с именем `currentWeek` или `partialWeek`
5. Убедитесь, что Service Account имеет доступ к таблице

## Логирование

Система логирует следующую информацию:
- Количество полученных строк из каждого диапазона
- Количество пустых строк с конца для каждого диапазона
- Результаты проверки (найденные проблемы)
- Ошибки при работе с API

## Обработка ошибок

- При ошибке доступа к именованному диапазону возвращается пустой массив проблем
- Ошибки логируются, но не прерывают основной цикл мониторинга
- Если диапазон не найден, выводится соответствующее сообщение об ошибке

## Тестирование

Создан тестовый файл `tests/emptyRowsCheck.test.js` для проверки функциональности:

```bash
node tests/emptyRowsCheck.test.js
```

Тест проверяет:
- Корректность подсчета пустых строк
- Логику определения проблемных диапазонов  
- Форматирование уведомлений
- Интеграцию с RaidMonitor
