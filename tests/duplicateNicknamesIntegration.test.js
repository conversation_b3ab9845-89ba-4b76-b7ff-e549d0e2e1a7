const RaidMonitor = require('../src/services/raidMonitor');

describe('Duplicate Nicknames Integration Test', () => {
  let raidMonitor;
  let mockSheetsService;
  let mockNotificationService;

  beforeEach(() => {
    // Создаем моки для всех зависимостей
    mockSheetsService = {
      getClientsData: jest.fn(),
      initialize: jest.fn()
    };

    mockNotificationService = {
      sendNotification: jest.fn(),
      initialize: jest.fn()
    };

    const mockScheduleService = {
      getCombinedScheduleData: jest.fn().mockResolvedValue([]),
      initialize: jest.fn()
    };

    const mockInternalScheduleService = {
      getInternalScheduleData: jest.fn().mockResolvedValue([]),
      initialize: jest.fn()
    };

    const mockDynamicConfigService = {
      getAllowedRaidTypes: jest.fn().mockResolvedValue({}),
      initialize: jest.fn()
    };

    const mockNotifiedStore = {
      has: jest.fn().mockReturnValue(false),
      add: jest.fn(),
      initialize: jest.fn()
    };

    const mockIgnoredOrdersStore = {
      has: jest.fn().mockReturnValue(false),
      initialize: jest.fn()
    };

    // Создаем RaidMonitor с моками
    raidMonitor = new RaidMonitor();
    raidMonitor.sheetsService = mockSheetsService;
    raidMonitor.notificationService = mockNotificationService;
    raidMonitor.scheduleService = mockScheduleService;
    raidMonitor.internalScheduleService = mockInternalScheduleService;
    raidMonitor.dynamicConfigService = mockDynamicConfigService;
    raidMonitor.notifiedStore = mockNotifiedStore;
    raidMonitor.ignoredOrdersStore = mockIgnoredOrdersStore;
    raidMonitor.initialized = true;
  });

  test('should detect and notify about duplicate nicknames', async () => {
    // Подготавливаем тестовые данные с дублирующимися никнеймами
    const clientsData = [
      {
        rowNumber: 100,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER1',
        team: 'A1',
        rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
      },
      {
        rowNumber: 101,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER2',
        team: 'A1',
        rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
      },
      {
        rowNumber: 102,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER3',
        team: 'A1',
        rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'uniqueplayer-server'; return arr; })()
      }
    ];

    mockSheetsService.getClientsData.mockResolvedValue(clientsData);

    // Вызываем проверку дублирующихся никнеймов
    const duplicates = await raidMonitor.checkDuplicateNicknames(clientsData);

    // Проверяем, что дубликаты найдены
    expect(duplicates).toHaveLength(1);
    expect(duplicates[0].nickname).toBe('testplayer-server');
    expect(duplicates[0].count).toBe(2);
    expect(duplicates[0].date).toBe('12.09.2025');
    expect(duplicates[0].time).toBe('20:30');
    expect(duplicates[0].raidType).toBe('MO Heroic');
    expect(duplicates[0].clients).toHaveLength(2);
    expect(duplicates[0].clients[0].rowNumber).toBe(100);
    expect(duplicates[0].clients[1].rowNumber).toBe(101);
  });

  test('should format notification correctly', async () => {
    const duplicates = [
      {
        nickname: 'testplayer-server',
        count: 2,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        clients: [
          { rowNumber: 100, orderId: 'ORDER1' },
          { rowNumber: 101, orderId: 'ORDER2' }
        ]
      }
    ];

    const notification = raidMonitor.formatDuplicateNicknamesNotification(duplicates);

    expect(notification).toContain('⚠️ <b>Дублирующиеся никнеймы в рейдах!</b>');
    expect(notification).toContain('❌ Проверьте <b>testplayer-server</b>, записан <b>2 раза</b> в рейд:');
    expect(notification).toContain('📅 <b>12.09.2025 20:30 MO Heroic</b>');
    expect(notification).toContain('📝 Строки: 100, 101');
  });

  test('should not detect duplicates when nicknames are in different raids', async () => {
    const clientsData = [
      {
        rowNumber: 100,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER1',
        team: 'A1',
        rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
      },
      {
        rowNumber: 101,
        date: '12.09.2025',
        time: '21:30',
        raidType: 'MO Normal',
        orderId: 'ORDER2',
        team: 'A2',
        rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
      }
    ];

    mockSheetsService.getClientsData.mockResolvedValue(clientsData);

    const duplicates = await raidMonitor.checkDuplicateNicknames(clientsData);

    expect(duplicates).toHaveLength(0);
  });

  test('should handle empty or missing nicknames gracefully', async () => {
    const clientsData = [
      {
        rowNumber: 100,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER1',
        team: 'A1',
        rawData: new Array(17).fill('') // Пустой никнейм
      },
      {
        rowNumber: 101,
        date: '12.09.2025',
        time: '20:30',
        raidType: 'MO Heroic',
        orderId: 'ORDER2',
        team: 'A1'
        // rawData отсутствует
      }
    ];

    mockSheetsService.getClientsData.mockResolvedValue(clientsData);

    const duplicates = await raidMonitor.checkDuplicateNicknames(clientsData);

    expect(duplicates).toHaveLength(0);
  });
});
