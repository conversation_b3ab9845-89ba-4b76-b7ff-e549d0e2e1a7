const RaidUtils = require('../src/utils/raidUtils');
const RaidMonitor = require('../src/services/raidMonitor');

describe('RaidUtils - Duplicate Nicknames Check', () => {
  describe('checkDuplicateNicknames', () => {
    test('should detect duplicate nicknames in same raid', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 102,
              orderId: 'ORDER3',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'differentplayer-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(1);
      expect(duplicates[0].nickname).toBe('testplayer-server');
      expect(duplicates[0].count).toBe(2);
      expect(duplicates[0].date).toBe('12.09.2025');
      expect(duplicates[0].time).toBe('20:30');
      expect(duplicates[0].raidType).toBe('MO Heroic');
      expect(duplicates[0].clients).toHaveLength(2);
      expect(duplicates[0].clients[0].rowNumber).toBe(100);
      expect(duplicates[0].clients[1].rowNumber).toBe(101);
    });

    test('should not detect duplicates in different raids', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        },
        '12.09.2025|21:30|MO Normal': {
          date: '12.09.2025',
          time: '21:30',
          raidType: 'MO Normal',
          clients: [
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(0);
    });

    test('should ignore empty nicknames', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: Array(17).fill('')
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: Array(17).fill('')
            },
            {
              rowNumber: 102,
              orderId: 'ORDER3',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(0);
    });

    test('should handle missing rawData gracefully', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1'
              // rawData отсутствует
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: null
            },
            {
              rowNumber: 102,
              orderId: 'ORDER3',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(0);
    });

    test('should detect multiple sets of duplicates', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'player1-server'; return arr; })()
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'player1-server'; return arr; })()
            },
            {
              rowNumber: 102,
              orderId: 'ORDER3',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'player2-server'; return arr; })()
            },
            {
              rowNumber: 103,
              orderId: 'ORDER4',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'player2-server'; return arr; })()
            },
            {
              rowNumber: 104,
              orderId: 'ORDER5',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'player3-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(2);
      
      const player1Duplicate = duplicates.find(d => d.nickname === 'player1-server');
      const player2Duplicate = duplicates.find(d => d.nickname === 'player2-server');
      
      expect(player1Duplicate).toBeDefined();
      expect(player1Duplicate.count).toBe(2);
      expect(player2Duplicate).toBeDefined();
      expect(player2Duplicate.count).toBe(2);
    });

    test('should handle single client groups', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(0);
    });

    test('should handle empty client groups', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: []
        }
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(0);
    });

    test('should ignore orders from ignore list', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'IGNORED_ORDER',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 101,
              orderId: 'NORMAL_ORDER',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      // Мокаем ignoredOrdersStore
      const mockIgnoredOrdersStore = {
        has: jest.fn((orderId) => orderId === 'IGNORED_ORDER')
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups, mockIgnoredOrdersStore);

      expect(duplicates).toHaveLength(0); // Дубликат не должен быть найден, так как один заказ игнорируется
      expect(mockIgnoredOrdersStore.has).toHaveBeenCalledWith('IGNORED_ORDER');
      expect(mockIgnoredOrdersStore.has).toHaveBeenCalledWith('NORMAL_ORDER');
    });

    test('should detect duplicates when ignored orders are not involved', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 102,
              orderId: 'IGNORED_ORDER',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'differentplayer-server'; return arr; })()
            }
          ]
        }
      };

      // Мокаем ignoredOrdersStore
      const mockIgnoredOrdersStore = {
        has: jest.fn((orderId) => orderId === 'IGNORED_ORDER')
      };

      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups, mockIgnoredOrdersStore);

      expect(duplicates).toHaveLength(1); // Дубликат должен быть найден между ORDER1 и ORDER2
      expect(duplicates[0].nickname).toBe('testplayer-server');
      expect(duplicates[0].count).toBe(2);
      expect(duplicates[0].clients).toHaveLength(2);
      expect(duplicates[0].clients[0].orderId).toBe('ORDER1');
      expect(duplicates[0].clients[1].orderId).toBe('ORDER2');
    });

    test('should work without ignoredOrdersStore parameter', () => {
      const clientGroups = {
        '12.09.2025|20:30|MO Heroic': {
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            {
              rowNumber: 100,
              orderId: 'ORDER1',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            },
            {
              rowNumber: 101,
              orderId: 'ORDER2',
              rawData: (() => { const arr = new Array(17).fill(''); arr[16] = 'testplayer-server'; return arr; })()
            }
          ]
        }
      };

      // Вызываем без ignoredOrdersStore (должно работать как раньше)
      const duplicates = RaidUtils.checkDuplicateNicknames(clientGroups);

      expect(duplicates).toHaveLength(1);
      expect(duplicates[0].nickname).toBe('testplayer-server');
      expect(duplicates[0].count).toBe(2);
    });
  });

  describe('formatDuplicateNicknamesNotification', () => {
    let raidMonitor;

    beforeEach(() => {
      raidMonitor = new RaidMonitor();
    });

    test('should format notification for single duplicate', () => {
      const duplicates = [
        {
          nickname: 'testplayer-server',
          count: 2,
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            { rowNumber: 100, orderId: 'ORDER1' },
            { rowNumber: 101, orderId: 'ORDER2' }
          ]
        }
      ];

      const notification = raidMonitor.formatDuplicateNicknamesNotification(duplicates);

      expect(notification).toContain('⚠️ <b>Дублирующиеся никнеймы в рейдах!</b>');
      expect(notification).toContain('❌ Проверьте <b>testplayer-server</b>, записан <b>2 раза</b> в рейд:');
      expect(notification).toContain('📅 <b>12.09.2025 20:30 MO Heroic</b>');
      expect(notification).toContain('📝 Строки: 100, 101');
    });

    test('should format notification for multiple duplicates', () => {
      const duplicates = [
        {
          nickname: 'player1-server',
          count: 2,
          date: '12.09.2025',
          time: '20:30',
          raidType: 'MO Heroic',
          clients: [
            { rowNumber: 100, orderId: 'ORDER1' },
            { rowNumber: 101, orderId: 'ORDER2' }
          ]
        },
        {
          nickname: 'player2-server',
          count: 3,
          date: '13.09.2025',
          time: '21:00',
          raidType: 'MO Normal',
          clients: [
            { rowNumber: 200, orderId: 'ORDER3' },
            { rowNumber: 201, orderId: 'ORDER4' },
            { rowNumber: 202, orderId: 'ORDER5' }
          ]
        }
      ];

      const notification = raidMonitor.formatDuplicateNicknamesNotification(duplicates);

      expect(notification).toContain('⚠️ <b>Дублирующиеся никнеймы в рейдах!</b>');
      expect(notification).toContain('❌ Проверьте <b>player1-server</b>, записан <b>2 раза</b> в рейд:');
      expect(notification).toContain('📅 <b>12.09.2025 20:30 MO Heroic</b>');
      expect(notification).toContain('📝 Строки: 100, 101');
      expect(notification).toContain('❌ Проверьте <b>player2-server</b>, записан <b>3 раза</b> в рейд:');
      expect(notification).toContain('📅 <b>13.09.2025 21:00 MO Normal</b>');
      expect(notification).toContain('📝 Строки: 200, 201, 202');
    });

    test('should return empty string for no duplicates', () => {
      const duplicates = [];
      const notification = raidMonitor.formatDuplicateNicknamesNotification(duplicates);
      expect(notification).toBe('');
    });
  });
});
