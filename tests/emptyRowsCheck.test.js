const SheetsService = require('../src/services/sheetsService');
const RaidMonitor = require('../src/services/raidMonitor');

// Мок для тестирования
class MockSheetsService extends SheetsService {
  constructor() {
    super();
    this.initialized = true;
  }

  async getNamedRangeData(spreadsheetId, rangeName) {
    // Мокаем данные для тестирования
    if (rangeName === 'currentWeek') {
      return [
        ['data1', 'data2', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 'data20', 'data21', 'data22'], // заполненная строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
      ];
    } else if (rangeName === 'partialWeek') {
      return [
        ['data1', 'data2', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 'data20', 'data21', 'data22'], // заполненная строка
        ['data1', 'data2', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 'data20', 'data21', 'data22'], // заполненная строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // пустая строка
      ];
    }
    return [];
  }
}

async function testEmptyRowsCheck() {
  console.log('🧪 Тестирование проверки пустых строк...\n');

  const mockSheetsService = new MockSheetsService();
  
  try {
    // Тест 1: Проверка currentWeek (должно быть 9 пустых строк с конца - меньше 10)
    console.log('📊 Тест 1: Проверка currentWeek');
    const currentWeekResult = await mockSheetsService.checkEmptyRowsInNamedRange('test-id', 'currentWeek');
    console.log('Результат currentWeek:', currentWeekResult);
    console.log(`✅ currentWeek: ${currentWeekResult.emptyRowsFromEnd} пустых строк с конца (ожидалось 9)\n`);

    // Тест 2: Проверка partialWeek (должно быть 10 пустых строк с конца - равно 10)
    console.log('📊 Тест 2: Проверка partialWeek');
    const partialWeekResult = await mockSheetsService.checkEmptyRowsInNamedRange('test-id', 'partialWeek');
    console.log('Результат partialWeek:', partialWeekResult);
    console.log(`✅ partialWeek: ${partialWeekResult.emptyRowsFromEnd} пустых строк с конца (ожидалось 10)\n`);

    // Тест 3: Проверка обоих диапазонов
    console.log('📊 Тест 3: Проверка обоих диапазонов');
    const bothRangesResult = await mockSheetsService.checkEmptyRowsInWeekRanges();
    console.log('Результат обоих диапазонов:', bothRangesResult);

    // Тест 4: Проверка логики RaidMonitor
    console.log('📊 Тест 4: Проверка логики RaidMonitor');
    const mockRaidMonitor = {
      sheetsService: mockSheetsService,
      async checkEmptyRowsInWeekRanges() {
        const weekRangesData = await this.sheetsService.checkEmptyRowsInWeekRanges();
        const issues = [];
        
        if (weekRangesData.currentWeek.emptyRowsFromEnd < 10) {
          issues.push({
            rangeName: 'currentWeek',
            emptyRows: weekRangesData.currentWeek.emptyRowsFromEnd,
            totalRows: weekRangesData.currentWeek.totalRows,
            threshold: 10
          });
        }
        
        if (weekRangesData.partialWeek.emptyRowsFromEnd < 10) {
          issues.push({
            rangeName: 'partialWeek',
            emptyRows: weekRangesData.partialWeek.emptyRowsFromEnd,
            totalRows: weekRangesData.partialWeek.totalRows,
            threshold: 10
          });
        }
        
        return issues;
      },
      formatEmptyRowsNotification(issues) {
        let message = '⚠️ <b>Недостаток пустых строк в диапазонах:</b>\n\n';
        
        issues.forEach(issue => {
          const rangeDisplayName = issue.rangeName === 'currentWeek' ? 'Текущая неделя' : 'Частичная неделя';
          message += `📊 <b>${rangeDisplayName} (${issue.rangeName})</b>\n`;
          message += `   • Пустых строк с конца: ${issue.emptyRows}\n`;
          message += `   • Требуется минимум: ${issue.threshold}\n`;
          message += `   • Общее количество строк: ${issue.totalRows}\n\n`;
        });
        
        message += '<i>Строка считается пустой, если столбцы B, E, G, O, Q, T, U, V не заполнены</i>';
        
        return message;
      }
    };

    const issues = await mockRaidMonitor.checkEmptyRowsInWeekRanges();
    console.log(`Найдено проблем: ${issues.length}`);
    
    if (issues.length > 0) {
      const notification = mockRaidMonitor.formatEmptyRowsNotification(issues);
      console.log('\n📢 Уведомление:');
      console.log(notification);
    }

    console.log('\n🎉 Все тесты прошли успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка в тестах:', error.message);
  }
}

// Запускаем тесты
testEmptyRowsCheck();
