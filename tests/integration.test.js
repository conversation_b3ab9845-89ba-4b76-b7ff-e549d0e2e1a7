const RaidMonitor = require('../src/services/raidMonitor');

// Мок для NotificationService
const mockNotificationService = {
  sendNotification: jest.fn(),
  initialized: true
};

// Мок для DateUtils
jest.mock('../src/utils/dateUtils', () => ({
  now: () => ({
    diff: jest.fn(() => 0), // Всегда возвращаем 0 дней разницы
    clone: jest.fn(() => ({
      subtract: jest.fn(() => ({
        minutes: jest.fn(() => 10),
        hours: jest.fn(() => 20),
        date: jest.fn(() => 29),
        month: jest.fn(() => 6), // July (0-based)
        isBefore: jest.fn(() => false)
      }))
    }))
  }),
  createDateTime: jest.fn((date, time) => ({
    diff: jest.fn(() => 0),
    clone: jest.fn(() => ({
      subtract: jest.fn(() => ({
        minutes: jest.fn(() => 10),
        hours: jest.fn(() => 20),
        date: jest.fn(() => 29),
        month: jest.fn(() => 6),
        isBefore: jest.fn(() => false)
      }))
    }))
  })),
  formatDateTime: jest.fn(() => '29.07.2025 20:10'),
  isDateTodayOrFuture: jest.fn(() => true) // Добавляем мок для новой функции
}));

// Мок для cron
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  }))
}));

describe('RaidMonitor Integration', () => {
  let raidMonitor;

  beforeEach(() => {
    raidMonitor = new RaidMonitor(mockNotificationService);
    
    // Мокаем сервисы
    raidMonitor.sheetsService = {
      getClientsData: jest.fn(() => Promise.resolve([]))
    };
    
    raidMonitor.scheduleService = {
      getScheduleData: jest.fn(() => Promise.resolve([
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          status: 'открыт'
        },
        {
          date: '29.07.2025',
          time: '21:00',
          raidType: 'LoU Normal',
          status: 'закрыт'
        }
      ]))
    };
    
    raidMonitor.internalScheduleService = {
      getInternalScheduleData: jest.fn(() => Promise.resolve([
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'h2s'
        },
        {
          date: '29.07.2025',
          time: '21:00',
          raidType: 'LoU Normal',
          team: 'a2s'
        }
      ]))
    };
    
    raidMonitor.initialized = true;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('checkTeamMismatches', () => {
    test('should detect team mismatches correctly', async () => {
      const clients = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'a2s', orderId: 'ZT26001', rowNumber: 1 },
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s', orderId: 'ZT26002', rowNumber: 2 }
      ];

      const internalSchedule = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s' }
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('a2s');
      expect(mismatches[0].orderId).toBe('ZT26001');
      expect(mismatches[0].expectedTeams).toContain('h2s');
      expect(mismatches[0].rowNumber).toBe(1);
    });

    test('should not report mismatches for correct teams', async () => {
      const clients = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s', orderId: 'ZT26003', rowNumber: 1 }
      ];

      const internalSchedule = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s' }
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      expect(mismatches).toHaveLength(0);
    });

    test('should ignore empty teams', async () => {
      const clients = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: '', orderId: 'ZT26004', rowNumber: 1 },
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: null, orderId: 'ZT26005', rowNumber: 2 }
      ];

      const internalSchedule = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s' }
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      expect(mismatches).toHaveLength(0);
    });

    test('should not report mismatch when schedule team not in config but client team is in config', async () => {
      // Мокаем getConfiguredTeams для возврата списка команд из конфига
      raidMonitor.dynamicConfigService.getConfiguredTeams = jest.fn().mockResolvedValue(['h2s', 'a2s', 'a1', 'h1']);

      const clients = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'a1', orderId: 'ZT26006', rowNumber: 1 }
      ];

      const internalSchedule = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'testteam' } // testteam не в конфиге
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      // Не должно быть несоответствий, так как в расписании команда НЕ из конфига, а у клиента команда ИЗ конфига
      expect(mismatches).toHaveLength(0);
    });

    test('should report mismatch when both teams are in config but different', async () => {
      // Мокаем getConfiguredTeams для возврата списка команд из конфига
      raidMonitor.dynamicConfigService.getConfiguredTeams = jest.fn().mockResolvedValue(['h2s', 'a2s', 'a1', 'h1']);

      const clients = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'a1', orderId: 'ZT26007', rowNumber: 1 }
      ];

      const internalSchedule = [
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s' } // h2s в конфиге
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      // Должно быть несоответствие, так как обе команды из конфига, но разные
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('a1');
      expect(mismatches[0].expectedTeams).toContain('h2s');
    });
  });

  describe('setupDynamicTeamChecks', () => {
    test('should create dynamic cron jobs for future raids', async () => {
      const cron = require('node-cron');

      await raidMonitor.setupDynamicTeamChecks();

      expect(cron.schedule).toHaveBeenCalled();
      expect(raidMonitor.dynamicCronJobs.size).toBeGreaterThan(0);
    });
  });

  describe('checkAllTeamsManual', () => {
    test('should return report for manual team check', async () => {
      // Мокаем данные для тестирования
      raidMonitor.sheetsService.getClientsData = jest.fn(() => Promise.resolve([
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s', orderId: 'ZT26010', rowNumber: 1 },
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'wrong_team', orderId: 'ZT26011', rowNumber: 2 },
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: '', orderId: 'ZT26012', rowNumber: 3 }
      ]));

      const result = await raidMonitor.checkAllTeamsManual();

      expect(result).toContain('Отчет о проверке команд');
      expect(result).toContain('Проверено рейдов: 2'); // Теперь проверяется 2 рейда (открытый и закрытый)
      expect(result).toContain('Рейдов с проблемами: 1');
      expect(result).toContain('Пустых команд: 1');
      expect(result).toContain('Неправильных команд: 1');
    });

    test('should return success message when no issues found', async () => {
      // Мокаем данные без проблем
      raidMonitor.sheetsService.getClientsData = jest.fn(() => Promise.resolve([
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s', orderId: 'ZT26020', rowNumber: 1 }
      ]));

      const result = await raidMonitor.checkAllTeamsManual();

      expect(result).toContain('Все команды заполнены правильно!');
    });

    test('should handle no raids scenario', async () => {
      // Мокаем пустое расписание
      raidMonitor.scheduleService.getScheduleData = jest.fn(() => Promise.resolve([]));

      const result = await raidMonitor.checkAllTeamsManual();

      expect(result).toContain('Нет открытых рейдов начиная с сегодняшнего дня');
    });

    test('should check both open and closed raids', async () => {
      // Мокаем данные с закрытым рейдом, в котором есть проблемы
      raidMonitor.sheetsService.getClientsData = jest.fn(() => Promise.resolve([
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'h2s', orderId: 'ZT26030', rowNumber: 1 },
        { date: '29.07.2025', time: '21:00', raidType: 'LoU Normal', team: 'wrong_team', orderId: 'ZT26031', rowNumber: 2 }
      ]));

      const result = await raidMonitor.checkAllTeamsManual();

      expect(result).toContain('Проверено рейдов: 2');
      expect(result).toContain('открытых: 1, закрытых: 1');
      expect(result).toContain('Рейдов с проблемами: 1');
      expect(result).toContain('Статус: закрыт'); // Проверяем, что закрытый рейд тоже проверяется
    });

    test('should include order IDs in team mismatch reports', async () => {
      // Мокаем данные с неправильной командой
      raidMonitor.sheetsService.getClientsData = jest.fn(() => Promise.resolve([
        { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'wrong_team', orderId: 'ZT26277', rowNumber: 331 }
      ]));

      const result = await raidMonitor.checkAllTeamsManual();

      expect(result).toContain('Заказ: "ZT26277"');
      expect(result).toContain('Строка 331');
      expect(result).toContain('команда "wrong_team" не соответствует расписанию');
    });
  });
});
