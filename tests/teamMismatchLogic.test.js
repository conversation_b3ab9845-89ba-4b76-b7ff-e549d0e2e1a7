const RaidMonitor = require('../src/services/raidMonitor');
const DynamicConfigService = require('../src/services/dynamicConfigService');

describe('Team Mismatch Logic Tests', () => {
  let raidMonitor;
  let mockDynamicConfigService;

  beforeEach(() => {
    // Создаем мок для DynamicConfigService
    mockDynamicConfigService = {
      getAllowedRaidTypes: jest.fn().mockResolvedValue({}),
      getConfiguredTeams: jest.fn().mockResolvedValue(['h2s', 'a2s', 'a1', 'h1'])
    };

    // Создаем RaidMonitor с мокнутым сервисом
    raidMonitor = new RaidMonitor(null, null, null, null, null, null);
    raidMonitor.dynamicConfigService = mockDynamicConfigService;
    raidMonitor.ignoredOrdersStore = new Set();
  });

  describe('New team mismatch logic', () => {
    test('should NOT send notification when schedule team NOT in config but client team IS in config', async () => {
      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'a1', // команда ИЗ конфига
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];
      
      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'testteam' // команда НЕ из конфига
        }
      ];
      
      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };
      
      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);
      
      // Не должно быть несоответствий
      expect(mismatches).toHaveLength(0);
    });

    test('should send notification when both teams are in config but different', async () => {
      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'a1', // команда ИЗ конфига
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];
      
      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'h2s' // команда ИЗ конфига, но другая
        }
      ];
      
      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };
      
      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);
      
      // Должно быть несоответствие
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('a1');
      expect(mismatches[0].expectedTeams).toContain('h2s');
    });

    test('should send notification when schedule team is in config but client team is NOT in config', async () => {
      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'randomteam', // команда НЕ из конфига
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];
      
      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'h2s' // команда ИЗ конфига
        }
      ];
      
      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };
      
      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);
      
      // Должно быть несоответствие
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('randomteam');
      expect(mismatches[0].expectedTeams).toContain('h2s');
    });

    test('should send notification when both teams are NOT in config', async () => {
      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'randomteam1', // команда НЕ из конфига
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];
      
      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'randomteam2' // команда НЕ из конфига
        }
      ];
      
      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };
      
      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);
      
      // Должно быть несоответствие
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('randomteam1');
      expect(mismatches[0].expectedTeams).toContain('randomteam2');
    });

    test('should NOT send notification when teams match exactly', async () => {
      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'a1', // команда ИЗ конфига
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];
      
      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'a1' // та же команда
        }
      ];
      
      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };
      
      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);
      
      // Не должно быть несоответствий
      expect(mismatches).toHaveLength(0);
    });

    test('should handle empty configured teams gracefully', async () => {
      // Мокаем getConfiguredTeams для возврата пустого массива
      mockDynamicConfigService.getConfiguredTeams = jest.fn().mockResolvedValue([]);

      const clients = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'a1',
          orderId: 'ZT26001',
          rowNumber: 1
        }
      ];

      const internalSchedule = [
        {
          date: '29.07.2025',
          time: '20:00',
          raidType: 'LoU Heroic',
          team: 'testteam'
        }
      ];

      const raid = { date: '29.07.2025', time: '20:00', raidType: 'LoU Heroic' };

      const mismatches = await raidMonitor.checkTeamMismatches(clients, internalSchedule, raid);

      // Должно быть несоответствие, так как нет конфигурации команд
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientTeam).toBe('a1');
      expect(mismatches[0].expectedTeams).toContain('testteam');
    });
  });
});
